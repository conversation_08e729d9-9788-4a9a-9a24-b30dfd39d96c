// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.Backend.Commands.SyncInventoryCommand
using Assets.Scripts.DataHelpers;
using Assets.Scripts.Utils;
using caravan.protobuf.messages;
using System;

namespace Assets.Scripts.Backend.Commands
{
    public class SyncInventoryCommand : Command
    {
        public SyncInventoryCommand(Action syncReplied = null)
        {
            this._action = syncReplied;
        }

        public override GenericMessage CreateBackendGenericMessage()
        {
            GenericMessage genericMessage = base.CreateGenericMessage(MessageTypes.SYNC_INVENTORY_MSG);
            SyncInventoryMessage syncInventoryMessage = new SyncInventoryMessage();
            this._inventoryMetaData = SyncInventoryCommand.CreateInventoryMetaData();
            syncInventoryMessage.inventory = this._inventoryMetaData;
            Command.SerializeCommandIntoGenericMessage<SyncInventoryMessage>(syncInventoryMessage, genericMessage);
            this._messageCreatedIn = FocusListener.FocusCounter;
            return genericMessage;
        }

        public override void FailedAndNoReply(bool isThereAChanceServerRecievedTheRequest)
        {
            if (this._action != null)
            {
                this._action();
            }
        }

        public override bool IsValid()
        {
            bool flag = InventoryHelper.Instance.AreThereUnsyncedItems();
            if (!flag && this._action != null)
            {
                this._action();
            }
            return flag;
        }

        public static InventoryMetaData CreateInventoryMetaData()
        {
            InventoryHelper instance = InventoryHelper.Instance;
            InventoryMetaData inventoryMetaData = new InventoryMetaData
            {
                anvil = instance.GetItemAmount(InventoryItemType.Anvil),
                bomb = instance.GetItemAmount(InventoryItemType.Bomb),
                boxingGlove = instance.GetItemAmount(InventoryItemType.BoxingGlove),
                coins = instance.GetItemAmount(InventoryItemType.Coins),
                newCoin = instance.GetItemAmount(InventoryItemType.NewCoin),
                discoBall = instance.GetItemAmount(InventoryItemType.DiscoBall),
                shuffle = instance.GetItemAmount(InventoryItemType.Dice),
                extra5Moves = instance.GetItemAmount(InventoryItemType.Extra5Moves),
                hammer = instance.GetItemAmount(InventoryItemType.Hammer),
                rocket = instance.GetItemAmount(InventoryItemType.Rocket),
                userType = instance.GetItemAmount(InventoryItemType.UserType),
                stars = instance.GetItemAmount(InventoryItemType.Stars),
                lifeTime = LifeStatusHelper.Instance.GetSyncTime(),
                life = instance.GetItemAmount(InventoryItemType.Life)
            };
            if (!instance.IsSynced(InventoryItemType.UnlimitedLife))
            {
                inventoryMetaData.unlimitedLife = LifeStatusHelper.Instance.GetUnlimitedLifeTime();
                if (inventoryMetaData.unlimitedLife == 0)
                {
                    inventoryMetaData.unlimitedLife = -1;
                }
            }
            return inventoryMetaData;
        }

        public static bool IsSame(InventoryMetaData inventory)
        {
            return inventory.anvil == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Anvil) && inventory.bomb == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Bomb) && inventory.boxingGlove == InventoryHelper.Instance.GetItemAmount(InventoryItemType.BoxingGlove) && inventory.coins == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Coins) && inventory.discoBall == InventoryHelper.Instance.GetItemAmount(InventoryItemType.DiscoBall) && inventory.shuffle == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Dice) && inventory.extra5Moves == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Extra5Moves) && inventory.hammer == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Hammer) && inventory.rocket == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Rocket) && inventory.userType == InventoryHelper.Instance.GetItemAmount(InventoryItemType.UserType) && inventory.stars == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Stars) && inventory.life == InventoryHelper.Instance.GetItemAmount(InventoryItemType.Life);
        }

        public bool ShouldPerformed()
        {
            return InventoryHelper.Instance.AreThereUnsyncedItems();
        }

        protected override MessageTypes GetResponseMessageType()
        {
            return MessageTypes.SYNC_INVENTORY_RESPONSE_MSG;
        }

        protected override Type GetReplyClassType()
        {
            return typeof(SyncInventoryResponseMessage);
        }

        protected override void BuildReply(object reply, ResultCodes resultCode)
        {
            SyncInventoryResponseMessage result = reply as SyncInventoryResponseMessage;
            this.Reply = new SyncInventoryReply(this, resultCode.AsReplyReason(), result, this._inventoryMetaData, this._messageCreatedIn, this._action);
        }

        private InventoryMetaData _inventoryMetaData;

        private readonly Action _action;

        private int _messageCreatedIn;
    }
}